// Auto-generated TypeScript types from Prisma schema
// Generated on: 2025-08-16T19:03:20.114Z
// Do not edit this file manually

// ==================== ENUMS ====================

export enum RoleEnum {
  SUPER_ADMIN = "SUPER_ADMIN",
  ADMIN_HR = "ADMIN_HR",
  ADMIN_RECRUITMENT = "ADMIN_RECRUITMENT",
  OWNER = "OWNER",
  MANAGER = "MANAGER",
  EMPLOYEE = "EMPLOYEE",
  CANDIDATE = "CANDIDATE",
}

export enum PermissionEnum {
  CAN_CREATE = "CAN_CREATE",
  CAN_EDIT = "CAN_EDIT",
  CAN_DELETE = "CAN_DELETE",
  CAN_READ = "CAN_READ",
}

export enum LeaveTypeEnum {
  ANNUAL = "ANNUAL",
  SICK = "SICK",
  MATERNITY = "MATERNITY",
  UNPAID = "UNPAID",
}

export enum LeaveStatusEnum {
  REQUESTED = "REQUESTED",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
  PENDING = "PENDING",
}

export enum BenefitTypeEnum {
  INSURANCE = "INSURANCE",
  VEHICLE = "VEHICLE",
}

export enum ApplicationStatusEnum {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  HIRED = "HIRED",
  REJECTED = "REJECTED",
}

export enum ProbationStatusEnum {
  IN_PROGRESS = "IN_PROGRESS",
  CONFIRMED = "CONFIRMED",
  EXTENDED = "EXTENDED",
}

export enum TaskStatusEnum {
  TODO = "TODO",
  IN_PROGRESS = "IN_PROGRESS",
  REVIEW = "REVIEW",
  COMPLETED = "COMPLETED",
  BLOCKED = "BLOCKED",
}

export enum TaskPriorityEnum {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

export enum TaxPaymentFrequencyEnum {
  MONTHLY = "MONTHLY",
  QUARTERLY = "QUARTERLY",
  ANNUALLY = "ANNUALLY",
}

export enum PayrollCycleEnum {
  MONTHLY = "MONTHLY",
  SEMI_MONTHLY = "SEMI_MONTHLY",
  BI_WEEKLY = "BI_WEEKLY",
  WEEKLY = "WEEKLY",
}

export enum BonusCalculationEnum {
  PERCENTAGE_OF_SALARY = "PERCENTAGE_OF_SALARY",
  FIXED_AMOUNT = "FIXED_AMOUNT",
  PERFORMANCE_BASED = "PERFORMANCE_BASED",
}

export enum JobOfferStatusEnum {
  DRAFT = "DRAFT",
  ACTIVE = "ACTIVE",
  EXPIRED = "EXPIRED",
  FILLED = "FILLED",
  CANCELLED = "CANCELLED",
}

export enum ContractTypeEnum {
  FULL_TIME = "FULL_TIME",
  PART_TIME = "PART_TIME",
  TEMPORARY = "TEMPORARY",
  CONTRACT = "CONTRACT",
  INTERNSHIP = "INTERNSHIP",
  REMOTE = "REMOTE",
  HYBRID = "HYBRID",
  ON_SITE = "ON_SITE",
  SEASONAL = "SEASONAL",
  FREELANCE = "FREELANCE",
}

export enum TrainingStatus {
  DRAFT = "DRAFT",
  SCHEDULED = "SCHEDULED",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  CANCELLED = "CANCELLED",
  POSTPONED = "POSTPONED",
}

export enum TrainingPriority {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

export enum ContractStatusEnum {
  DRAFT = "DRAFT",
  PENDING_APPROVAL = "PENDING_APPROVAL",
  ACTIVE = "ACTIVE",
  EXPIRED = "EXPIRED",
  TERMINATED = "TERMINATED",
  CANCELLED = "CANCELLED",
}

export enum DocumentApprovalStatusEnum {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
  CANCELLED = "CANCELLED",
}

export enum ReportTypeEnum {
  EMPLOYEE_LIST = "EMPLOYEE_LIST",
  PAYROLL_SUMMARY = "PAYROLL_SUMMARY",
  LEAVE_REPORT = "LEAVE_REPORT",
  ATTENDANCE_REPORT = "ATTENDANCE_REPORT",
  PERFORMANCE_REPORT = "PERFORMANCE_REPORT",
  RECRUITMENT_REPORT = "RECRUITMENT_REPORT",
  CUSTOM = "CUSTOM",
}

export enum ReportScheduleEnum {
  DAILY = "DAILY",
  WEEKLY = "WEEKLY",
  MONTHLY = "MONTHLY",
  QUARTERLY = "QUARTERLY",
  YEARLY = "YEARLY",
}

export enum EmploymentStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  TERMINATED = "TERMINATED",
  ON_LEAVE = "ON_LEAVE",
  SUSPENDED = "SUSPENDED",
}

export enum ReportExecutionStatusEnum {
  PENDING = "PENDING",
  RUNNING = "RUNNING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  CANCELLED = "CANCELLED",
}

// ==================== MODELS ====================

export interface User {
  id: string;
  email: string;
  password?: string | null;
  googleId?: string | null;
  verified: boolean;
  verificationOtp?: string | null;
  role: RoleEnum;
  passwordReset: any;
}

export interface Profile {
  id: string;
  firstName: string;
  lastName: string;
  avatar?: string | null;
  phoneNumber?: string | null;
  birthDate?: Date | null;
  sex?: string | null;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Company {
  id: string;
  createdById: string;
  companyName: string;
  email: string;
  phoneNumbers: string[];
  website?: string | null;
  logo?: string | null;
  officialName: string;
  taxIdentificationNumber: string;
  industry: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Document {
  id: string;
  name: string;
  type: string;
  url: string;
  description?: string | null;
  uploadedAt: Date;
  userId?: string | null;
  companyId?: string | null;
  employeeId?: string | null;
  contractId?: string | null;
  documentTemplateId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface CompanyTaxSettings {
  id: string;
  companyId: string;
  incomeTaxRate: number;
  socialSecurityRate: number;
  unEmploymentInsuranceRate: number;
  healthInsuranceRate: number;
  pensionContributionRate: number;
  incomeTaxThreshold?: number | null;
  socialSecurityThreshold?: number | null;
  standardDeduction?: number | null;
  familyAllowance?: number | null;
  taxPaymentFrequency?: TaxPaymentFrequencyEnum | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface PayrollConfiguration {
  id: string;
  companyId: string;
  payrollCycle?: PayrollCycleEnum | null;
  paymentDay: number;
  overtimeMultiplier: number;
  maxOvertimeHours?: number | null;
  bonusType?: BonusCalculationEnum | null;
  performanceBonusRate?: number | null;
  paidTimeOffAccrualRate?: number | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Address {
  id: string;
  street?: string | null;
  city?: string | null;
  postalCode?: string | null;
  country?: string | null;
  companyId?: string | null;
  userId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface JobOffer {
  id: string;
  title: string;
  description: string;
  publishDate: Date;
  expirationDate: Date;
  status: JobOfferStatusEnum;
  location: string;
  contractTypes: ContractTypeEnum[];
  minSalary?: number | null;
  maxSalary?: number | null;
  requiredSkills: string[];
  companyId: string;
  departmentId?: string | null;
  positionId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Application {
  id: string;
  applicationDate: Date;
  status: ApplicationStatusEnum;
  jobId: string;
  userId: string;
  coverLetter: string;
  resume: string;
  references?: string | null;
  additionalDocuments: string[];
  preferredStartDate?: Date | null;
  currentEmploymentStatus?: string | null;
  desiredSalary?: number | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApplicationResponse {
  id: string;
  applicationResponseDate: Date;
  content: string;
  applicationId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Department {
  id: string;
  name?: string | null;
  description?: string | null;
  companyId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Position {
  id: string;
  title: string;
  description?: string | null;
  departmentId: string;
  requiredSkills: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Task {
  id: string;
  title: string;
  description?: string | null;
  dueDate: Date;
  status: TaskStatusEnum;
  priority: TaskPriorityEnum;
  assignedToId?: string | null;
  createdById: string;
  startDate?: Date | null;
  completedDate?: Date | null;
  parentTaskId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Employee {
  id: string;
  userId: string;
  status: EmploymentStatus;
  hireDate: Date;
  companyId: string;
  departmentId?: string | null;
  positionId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Payslip {
  id: string;
  month: number;
  year: number;
  grossSalary: number;
  taxDeductions: number;
  socialSecurity: number;
  otherDeductions: number;
  netSalary: number;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Salary {
  id: string;
  baseSalary: number;
  housingAllowance: number;
  transportAllowance: number;
  bonus: number;
  overtimeHours: number;
  overtimeRate: number;
  effectiveDate: Date;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Leave {
  id: string;
  leaveType: LeaveTypeEnum;
  startDate: Date;
  endDate: Date;
  reason?: string | null;
  status: LeaveStatusEnum;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Timesheet {
  id: string;
  periodStart: Date;
  periodEnd: Date;
  totalRegularHours: number;
  totalOvertimeHours: number;
  totalUndertimeHours: number;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkDay {
  id: string;
  date: Date;
  arrivalTime?: Date | null;
  departureTime?: Date | null;
  regularHours: number;
  overtimeHours: number;
  undertimeHours: number;
  timesheetId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PerformanceEvaluation {
  id: string;
  periodStart: Date;
  periodEnd: Date;
  goals: string;
  selfEvaluation: string;
  managerEvaluation: string;
  employeeId: string;
}

export interface Training {
  id: string;
  trainingName: string;
  description: string;
  startDate: Date;
  endDate: Date;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IssuedObject {
  id: string;
  objectName: string;
  description: string;
  issuedDate: Date;
  returnDate: Date;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface DisciplinaryAction {
  id: string;
  actionDate: Date;
  description: string;
  actionTaken: string;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Complaint {
  id: string;
  complaintDate: Date;
  description: string;
  resolutionDate: Date;
  resolutionDetails: string;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Benefit {
  id: string;
  benefitType: BenefitTypeEnum;
  description: string;
  authorizedAmount: number;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ExpenseReport {
  id: string;
  description: string;
  amount: number;
  reportDate: Date;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface JobVacancy {
  id: string;
  jobTitle: string;
  description: string;
  openingDate: Date;
  closingDate: Date;
  companyId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProbationPeriod {
  id: string;
  startDate: Date;
  endDate: Date;
  status: ProbationStatusEnum;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Recommendation {
  id: string;
  description: string;
  recommendationDate: Date;
  senderEmployeeId: string;
  receiverEmployeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Departure {
  id: string;
  departureDate: Date;
  reason: string;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Induction {
  id: string;
  startDate: Date;
  endDate: Date;
  trainer: string;
  feedback: string;
  employeeId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Contract {
  id: string;
  contractNumber: string;
  title: string;
  contractType: ContractTypeEnum;
  status: ContractStatusEnum;
  startDate: Date;
  endDate?: Date | null;
  salary?: number | null;
  currency: string;
  workingHours?: number | null;
  probationPeriod?: number | null;
  noticePeriod?: number | null;
  renewalTerms?: string | null;
  terminationClause?: string | null;
  benefits: string[];
  responsibilities: string[];
  createdAt: Date;
  updatedAt: Date;
  signedAt?: Date | null;
  employeeId: string;
  companyId: string;
  templateId?: string | null;
}

export interface ContractTemplate {
  id: string;
  name: string;
  description?: string | null;
  content: string;
  contractType: ContractTypeEnum;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  companyId: string;
}

export interface DocumentTemplate {
  id: string;
  name: string;
  description?: string | null;
  content: string;
  category: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  companyId: string;
}

export interface DocumentApproval {
  id: string;
  status: DocumentApprovalStatusEnum;
  comments?: string | null;
  approvedAt?: Date | null;
  rejectedAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
  documentId: string;
  approverId: string;
  requesterId: string;
  employeeDataId?: string | null;
}

export interface CompanySettings {
  id: string;
  companyId: string;
  workingDaysPerWeek: number;
  workingHoursPerDay: number;
  timeZone: string;
  currency: string;
  language: string;
  annualLeaveEntitlement: number;
  sickLeaveEntitlement: number;
  maternityLeaveEntitlement: number;
  emailNotifications: boolean;
  smsNotifications: boolean;
  passwordMinLength: number;
  sessionTimeout: number;
  twoFactorAuth: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Report {
  id: string;
  name: string;
  description?: string | null;
  reportType: ReportTypeEnum;
  parameters: any;
}

export interface ReportExecution {
  id: string;
  status: ReportExecutionStatusEnum;
  startedAt: Date;
  completedAt?: Date | null;
  fileUrl?: string | null;
  errorMessage?: string | null;
  parameters: any;
}

// ==================== UTILITY TYPES ====================

export namespace Prisma {
  export type UserGetPayload<T> = User;
  export type EmployeeGetPayload<T> = Employee;
  export type CompanyGetPayload<T> = Company;
  export type JobOfferGetPayload<T> = JobOffer;
  export type ApplicationGetPayload<T> = Application;
  
  // Add more payload types as needed
  export type GetPayload<T, K> = any;
}

// ==================== USAGE EXAMPLES ====================

/*
Example usage:

import type { User, Employee, JobOffer } from '@samuelmbabhazi/hr-prisma-types';
import { RoleEnum, EmploymentStatus } from '@samuelmbabhazi/hr-prisma-types';

const user: User = {
  id: "123",
  email: "<EMAIL>",
  role: RoleEnum.EMPLOYEE,
  // ... other properties
};

const employee: Employee = {
  id: "456",
  userId: user.id,
  status: EmploymentStatus.ACTIVE,
  // ... other properties
};
*/