// Export all Prisma types and values
export * from '@prisma/client';

// Re-export specific types for better organization
export type {
  // User and Profile types
  User,
  Profile,

  // Company and Address types
  Company,
  Address,

  // Employee and HR types
  Employee,
  Department,
  Position,

  // Job and Application types
  JobOffer,
  JobVacancy,
  Application,
  ApplicationResponse,

  // Payroll and Salary types
  Salary,
  Payslip,
  PayrollConfiguration,
  CompanyTaxSettings,

  // Leave and Time tracking types
  Leave,
  Timesheet,
  WorkDay,

  // Performance and Training types
  PerformanceEvaluation,
  Training,
  Task,

  // Benefits and Expenses types
  Benefit,
  ExpenseReport,
  IssuedObject,

  // Disciplinary and Complaints types
  DisciplinaryAction,
  Complaint,

  // Contract and Document types
  Contract,
  ContractTemplate,
  Document,
  DocumentTemplate,
  DocumentApproval,

  // Reports and Analytics types
  Report,
  ReportExecution,

  // Other HR types
  ProbationPeriod,
  Recommendation,
  Departure,
  Induction,

  // Settings types
  CompanySettings,

  // Export Prisma utility types
  Prisma,
} from '@prisma/client';

// Re-export enums as values (not just types) so they can be used in runtime
export {
  RoleEnum,
  LeaveTypeEnum,
  LeaveStatusEnum,
  BenefitTypeEnum,
  ApplicationStatusEnum,
  ProbationStatusEnum,
  TaskStatusEnum,
  TaskPriorityEnum,
  TaxPaymentFrequencyEnum,
  PayrollCycleEnum,
  BonusCalculationEnum,
  JobOfferStatusEnum,
  ContractTypeEnum,
  TrainingStatus,
  ContractStatusEnum,
  DocumentApprovalStatusEnum,
  ReportTypeEnum,
  ReportScheduleEnum,
  EmploymentStatus,
  ReportExecutionStatusEnum,
} from '@prisma/client';
