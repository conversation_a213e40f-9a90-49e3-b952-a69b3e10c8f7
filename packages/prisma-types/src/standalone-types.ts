// Types autonomes générés automatiquement à partir du schéma Prisma
// Ces types peuvent être utilisés sans installer @prisma/client

// Types de base générés automatiquement
// Ces types sont utilisés quand @prisma/client n'est pas disponible

export interface User {
  id: string;
  email: string;
  password?: string | null;
  googleId?: string | null;
  verified: boolean;
  verificationOtp?: string | null;
  role: RoleEnum;
  passwordReset: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface Profile {
  id: string;
  firstName: string;
  lastName: string;
  avatar?: string | null;
  phoneNumber?: string | null;
  birthDate?: Date | null;
  sex?: string | null;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Employee {
  id: string;
  userId: string;
  status: EmploymentStatus;
  hireDate: Date;
  companyId: string;
  departmentId?: string | null;
  positionId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Company {
  id: string;
  createdById: string;
  companyName: string;
  email: string;
  phoneNumbers: string[];
  website?: string | null;
  logo?: string | null;
  officialName: string;
  taxIdentificationNumber: string;
  industry: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface JobOffer {
  id: string;
  title: string;
  description: string;
  publishDate: Date;
  expirationDate: Date;
  status: JobOfferStatusEnum;
  location: string;
  contractTypes: ContractTypeEnum[];
  minSalary?: number | null;
  maxSalary?: number | null;
  requiredSkills: string[];
  companyId: string;
  departmentId?: string | null;
  positionId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Enums
export enum RoleEnum {
  SUPER_ADMIN = "SUPER_ADMIN",
  ADMIN_HR = "ADMIN_HR",
  ADMIN_RECRUITMENT = "ADMIN_RECRUITMENT",
  OWNER = "OWNER",
  MANAGER = "MANAGER",
  EMPLOYEE = "EMPLOYEE",
  CANDIDATE = "CANDIDATE"
}

export enum EmploymentStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  TERMINATED = "TERMINATED",
  ON_LEAVE = "ON_LEAVE",
  SUSPENDED = "SUSPENDED"
}

export enum JobOfferStatusEnum {
  DRAFT = "DRAFT",
  ACTIVE = "ACTIVE",
  EXPIRED = "EXPIRED",
  FILLED = "FILLED",
  CANCELLED = "CANCELLED"
}

export enum ContractTypeEnum {
  FULL_TIME = "FULL_TIME",
  PART_TIME = "PART_TIME",
  TEMPORARY = "TEMPORARY",
  CONTRACT = "CONTRACT",
  INTERNSHIP = "INTERNSHIP",
  REMOTE = "REMOTE",
  HYBRID = "HYBRID",
  ON_SITE = "ON_SITE",
  SEASONAL = "SEASONAL",
  FREELANCE = "FREELANCE"
}

export enum LeaveTypeEnum {
  ANNUAL = "ANNUAL",
  SICK = "SICK",
  MATERNITY = "MATERNITY",
  UNPAID = "UNPAID"
}

export enum ApplicationStatusEnum {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  HIRED = "HIRED",
  REJECTED = "REJECTED"
}

// Types utilitaires de base
export namespace Prisma {
  export type UserGetPayload<T> = User;
  export type EmployeeGetPayload<T> = Employee;
  export type JobOfferGetPayload<T> = JobOffer;
}


// Note: Ces types sont générés automatiquement.
// Pour des types plus complets, installez @prisma/client dans votre projet.
