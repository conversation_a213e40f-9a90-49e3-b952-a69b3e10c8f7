name: Publish Package

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        registry-url: 'https://registry.npmjs.org'
        
    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: latest
        
    - name: Install dependencies
      run: |
        cd packages/prisma-types
        pnpm install
        
    - name: Build package
      run: |
        cd packages/prisma-types
        pnpm run build
        
    - name: Test package
      run: |
        cd packages/prisma-types
        npx tsc --noEmit example.ts
        
    - name: Publish to npm
      run: |
        cd packages/prisma-types
        npm publish --access public
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
