#!/bin/bash

# Script pour mettre à jour le schéma Prisma depuis le projet principal
# et regénérer les types

echo "🔄 Mise à jour du schéma Prisma..."

# Copier le schéma depuis le projet principal
cp ../../prisma/schema.prisma ./prisma/schema.prisma

if [ $? -eq 0 ]; then
    echo "✅ Schéma copié avec succès"
    
    echo "🔄 Génération des types Prisma..."
    npm run generate
    
    if [ $? -eq 0 ]; then
        echo "✅ Types générés avec succès"
        
        echo "🔄 Construction du package..."
        npm run build
        
        if [ $? -eq 0 ]; then
            echo "✅ Package construit avec succès"
            echo "🎉 Mise à jour terminée !"
        else
            echo "❌ Erreur lors de la construction"
            exit 1
        fi
    else
        echo "❌ Erreur lors de la génération des types"
        exit 1
    fi
else
    echo "❌ Erreur lors de la copie du schéma"
    exit 1
fi
