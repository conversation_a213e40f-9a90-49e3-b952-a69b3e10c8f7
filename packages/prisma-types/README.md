# @samuelmbabhazi/hr-prisma-types

Package de types TypeScript pour l'API Lumina HR basé sur le schéma Prisma.

## Installation

```bash
npm install @samuelmbabhazi/hr-prisma-types
# ou
yarn add @samuelmbabhazi/hr-prisma-types
# ou
pnpm add @samuelmbabhazi/hr-prisma-types
```

## Utilisation

```typescript
// Import des types
import type {
  User,
  Employee,
  Company,
  JobOffer,
  Prisma
} from '@samuelmbabhazi/hr-prisma-types';

// Import des enums (valeurs)
import {
  RoleEnum,
  EmploymentStatus,
  LeaveTypeEnum,
  ApplicationStatusEnum
} from '@samuelmbabhazi/hr-prisma-types';

// Utilisation des types
const user: User = {
  id: "550e8400-e29b-41d4-a716-446655440000",
  email: "<EMAIL>",
  password: null,
  googleId: null,
  verified: true,
  verificationOtp: null,
  role: RoleEnum.EMPLOYEE,
  passwordReset: { otp: null, expiresAt: null },
  createdAt: new Date(),
  updatedAt: new Date(),
};

const employee: Employee = {
  id: "550e8400-e29b-41d4-a716-446655440001",
  userId: user.id,
  status: EmploymentStatus.ACTIVE,
  hireDate: new Date("2023-01-15"),
  companyId: "550e8400-e29b-41d4-a716-446655440002",
  departmentId: null,
  positionId: null,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Utilisation des types Prisma pour les requêtes complexes
type UserWithProfile = Prisma.UserGetPayload<{
  include: {
    profile: true;
    employee: {
      include: {
        department: true;
        position: true;
      };
    };
  };
}>;
```

## Types disponibles

### Types principaux
- `User` - Utilisateur du système
- `Profile` - Profil utilisateur
- `Company` - Entreprise
- `Employee` - Employé
- `Department` - Département
- `Position` - Poste

### Types RH
- `JobOffer` - Offre d'emploi
- `Application` - Candidature
- `Salary` - Salaire
- `Payslip` - Fiche de paie
- `Leave` - Congé
- `Timesheet` - Feuille de temps
- `PerformanceEvaluation` - Évaluation de performance
- `Training` - Formation

### Types de contrats et documents
- `Contract` - Contrat
- `ContractTemplate` - Modèle de contrat
- `Document` - Document
- `DocumentTemplate` - Modèle de document
- `DocumentApproval` - Approbation de document

### Enums
- `RoleEnum` - Rôles utilisateur
- `EmploymentStatus` - Statut d'emploi
- `LeaveTypeEnum` - Types de congé
- `ContractTypeEnum` - Types de contrat
- `TaskStatusEnum` - Statuts de tâche
- Et bien d'autres...

## Développement

### Construction du package

```bash
cd packages/prisma-types
npm run build
```

### Mise à jour du schéma

Quand le schéma Prisma principal est modifié, utilisez :

```bash
npm run update-schema
# ou
./update-schema.sh
```

### Publication

```bash
# Mise à jour de version patch (1.0.0 -> 1.0.1)
npm run version:patch

# Mise à jour de version minor (1.0.0 -> 1.1.0)
npm run version:minor

# Mise à jour de version major (1.0.0 -> 2.0.0)
npm run version:major

# Publication sur npm
npm run publish:npm
```

### Scripts disponibles

- `npm run build` - Construit le package
- `npm run generate` - Génère les types Prisma
- `npm run dev` - Mode développement avec watch
- `npm run clean` - Nettoie les fichiers générés
- `npm run update-schema` - Met à jour le schéma depuis le projet principal

## Structure du package

```
packages/prisma-types/
├── src/
│   └── index.ts          # Point d'entrée principal
├── prisma/
│   └── schema.prisma     # Schéma Prisma (copie)
├── dist/
│   └── index.d.ts        # Types générés
├── package.json
├── tsconfig.json
├── README.md
├── example.ts            # Exemples d'utilisation
└── update-schema.sh      # Script de mise à jour
```

## Licence

ISC
