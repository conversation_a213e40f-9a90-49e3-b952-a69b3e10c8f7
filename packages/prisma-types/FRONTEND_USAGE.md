# Guide d'utilisation pour le Frontend

Ce guide explique comment utiliser le package `@samuelmbabhazi/hr-prisma-types` dans vos projets frontend (React, Vue, Angular, etc.).

## Installation

```bash
npm install @samuelmbabhazi/hr-prisma-types
```

## Utilisation avec React

### 1. Types pour les composants

```typescript
// components/UserProfile.tsx
import React from 'react';
import type { User, Profile } from '@samuelmbabhazi/hr-prisma-types';

interface UserProfileProps {
  user: User;
  profile: Profile;
}

export const UserProfile: React.FC<UserProfileProps> = ({ user, profile }) => {
  return (
    <div>
      <h2>{profile.firstName} {profile.lastName}</h2>
      <p>Email: {user.email}</p>
      <p>Rôle: {user.role}</p>
    </div>
  );
};
```

### 2. Types pour les hooks et API

```typescript
// hooks/useEmployees.ts
import { useState, useEffect } from 'react';
import type { Employee, Department, Position } from '@samuelmbabhazi/hr-prisma-types';
import { EmploymentStatus } from '@samuelmbabhazi/hr-prisma-types';

type EmployeeWithDetails = Employee & {
  user: {
    profile: Profile;
  };
  department: Department | null;
  position: Position | null;
};

export const useEmployees = () => {
  const [employees, setEmployees] = useState<EmployeeWithDetails[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        const response = await fetch('/api/employees');
        const data: EmployeeWithDetails[] = await response.json();
        
        // Filtrer seulement les employés actifs
        const activeEmployees = data.filter(emp => 
          emp.status === EmploymentStatus.ACTIVE
        );
        
        setEmployees(activeEmployees);
      } catch (error) {
        console.error('Erreur lors du chargement des employés:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEmployees();
  }, []);

  return { employees, loading };
};
```

### 3. Formulaires avec validation

```typescript
// components/JobOfferForm.tsx
import React, { useState } from 'react';
import type { JobOffer } from '@samuelmbabhazi/hr-prisma-types';
import { JobOfferStatusEnum, ContractTypeEnum } from '@samuelmbabhazi/hr-prisma-types';

interface JobOfferFormData {
  title: string;
  description: string;
  location: string;
  contractTypes: ContractTypeEnum[];
  minSalary?: number;
  maxSalary?: number;
  requiredSkills: string[];
  expirationDate: string;
}

export const JobOfferForm: React.FC = () => {
  const [formData, setFormData] = useState<JobOfferFormData>({
    title: '',
    description: '',
    location: '',
    contractTypes: [],
    requiredSkills: [],
    expirationDate: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const jobOfferData: Partial<JobOffer> = {
      ...formData,
      status: JobOfferStatusEnum.DRAFT,
      publishDate: new Date(),
      expirationDate: new Date(formData.expirationDate),
    };

    try {
      const response = await fetch('/api/job-offers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(jobOfferData),
      });
      
      if (response.ok) {
        console.log('Offre d\'emploi créée avec succès');
      }
    } catch (error) {
      console.error('Erreur:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Champs du formulaire */}
    </form>
  );
};
```

## Utilisation avec Vue.js

```typescript
// composables/useJobOffers.ts
import { ref, computed } from 'vue';
import type { JobOffer, Company } from '@samuelmbabhazi/hr-prisma-types';
import { JobOfferStatusEnum } from '@samuelmbabhazi/hr-prisma-types';

export const useJobOffers = () => {
  const jobOffers = ref<(JobOffer & { company: Company })[]>([]);
  const loading = ref(false);

  const activeJobOffers = computed(() =>
    jobOffers.value.filter(job => job.status === JobOfferStatusEnum.ACTIVE)
  );

  const fetchJobOffers = async () => {
    loading.value = true;
    try {
      const response = await fetch('/api/job-offers');
      jobOffers.value = await response.json();
    } catch (error) {
      console.error('Erreur:', error);
    } finally {
      loading.value = false;
    }
  };

  return {
    jobOffers,
    activeJobOffers,
    loading,
    fetchJobOffers,
  };
};
```

## Utilisation avec Angular

```typescript
// services/employee.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import type { Employee, Salary, Leave } from '@samuelmbabhazi/hr-prisma-types';
import { EmploymentStatus, LeaveStatusEnum } from '@samuelmbabhazi/hr-prisma-types';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService {
  constructor(private http: HttpClient) {}

  getEmployees(): Observable<Employee[]> {
    return this.http.get<Employee[]>('/api/employees');
  }

  getActiveEmployees(): Observable<Employee[]> {
    return this.http.get<Employee[]>('/api/employees', {
      params: { status: EmploymentStatus.ACTIVE }
    });
  }

  getEmployeeSalaries(employeeId: string): Observable<Salary[]> {
    return this.http.get<Salary[]>(`/api/employees/${employeeId}/salaries`);
  }

  requestLeave(leaveData: Partial<Leave>): Observable<Leave> {
    return this.http.post<Leave>('/api/leaves', {
      ...leaveData,
      status: LeaveStatusEnum.REQUESTED
    });
  }
}
```

## Types utilitaires pour les API

```typescript
// types/api.ts
import type { Prisma } from '@samuelmbabhazi/hr-prisma-types';

// Types pour les réponses API avec relations
export type UserWithProfile = Prisma.UserGetPayload<{
  include: { profile: true };
}>;

export type EmployeeWithDetails = Prisma.EmployeeGetPayload<{
  include: {
    user: { include: { profile: true } };
    department: true;
    position: true;
    salaries: { orderBy: { effectiveDate: 'desc' }; take: 1 };
  };
}>;

export type JobOfferWithApplications = Prisma.JobOfferGetPayload<{
  include: {
    company: true;
    applications: {
      include: {
        user: { include: { profile: true } };
      };
    };
  };
}>;

// Types pour les réponses API standardisées
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

## Bonnes pratiques

1. **Séparez les types et les valeurs** : Utilisez `import type` pour les types et `import` pour les enums.

2. **Créez des types composés** : Utilisez les types Prisma utilitaires pour créer des types complexes.

3. **Validez les données** : Utilisez les enums pour valider les valeurs côté frontend.

4. **Typage des API** : Créez des interfaces pour vos réponses API basées sur les types Prisma.

5. **Réutilisabilité** : Créez des types utilitaires réutilisables dans votre application.

## Mise à jour

Quand une nouvelle version du package est disponible :

```bash
npm update @samuelmbabhazi/hr-prisma-types
```

Vérifiez les changements dans le CHANGELOG et adaptez votre code si nécessaire.
