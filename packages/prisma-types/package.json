{"name": "@samuelmbabhazi/hr-prisma-types", "version": "1.0.1", "description": "Types package for Lumina HR API based on Prisma schema", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run generate && npm run generate-standalone && tsc", "generate": "prisma generate", "generate-standalone": "node scripts/generate-standalone-types.js", "prepare": "npm run build", "dev": "tsc --watch", "clean": "rimraf dist node_modules/.pnpm/@prisma", "update-schema": "cp ../../prisma/schema.prisma ./prisma/schema.prisma && npm run generate", "publish:npm": "npm publish --access public", "version:patch": "npm version patch", "version:minor": "npm version minor", "version:major": "npm version major"}, "keywords": ["hr", "prisma", "types", "typescript", "lumina"], "author": "<PERSON>", "license": "ISC", "packageManager": "pnpm@10.6.3", "devDependencies": {"@prisma/client": "^4.15.0", "prisma": "^4.15.0", "rimraf": "^3.0.2", "typescript": "^5.9.2"}, "peerDependencies": {"@prisma/client": "^4.15.0"}, "peerDependenciesMeta": {"@prisma/client": {"optional": true}}}