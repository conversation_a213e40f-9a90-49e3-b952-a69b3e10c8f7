{"name": "@samuelmbabhazi/hr-prisma-types", "version": "1.0.3", "description": "Standalone TypeScript types for Lumina HR API generated from Prisma schema", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run generate-types && tsc", "generate-types": "node scripts/generate-types-from-schema.js", "prepare": "npm run build", "dev": "npm run generate-types && tsc --watch", "clean": "rimraf dist src/generated-types.ts", "update-schema": "cp ../../prisma/schema.prisma ./prisma/schema.prisma && npm run generate-types", "publish:npm": "npm publish --access public", "version:patch": "npm version patch", "version:minor": "npm version minor", "version:major": "npm version major"}, "keywords": ["hr", "prisma", "types", "typescript", "lumina"], "author": "<PERSON>", "license": "ISC", "packageManager": "pnpm@10.6.3", "devDependencies": {"rimraf": "^3.0.2", "typescript": "^5.9.2"}}