#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Standalone TypeScript types generator from Prisma schema
 * Generates types without depending on @prisma/client
 */

console.log('🔄 Generating standalone types from Prisma schema...');

const schemaPath = path.join(__dirname, '../prisma/schema.prisma');
const outputPath = path.join(__dirname, '../src/generated-types.ts');

if (!fs.existsSync(schemaPath)) {
  console.error('❌ Prisma schema not found at:', schemaPath);
  process.exit(1);
}

const schemaContent = fs.readFileSync(schemaPath, 'utf8');

// Parse the schema
const models = parseModels(schemaContent);
const enums = parseEnums(schemaContent);

// Generate TypeScript types
const generatedTypes = generateTypeScript(models, enums);

// Write to file
fs.writeFileSync(outputPath, generatedTypes);

console.log('✅ Types generated successfully at:', outputPath);
console.log(`📊 Generated ${models.length} models and ${enums.length} enums`);

function parseModels(schema) {
  const models = [];
  const modelRegex = /model\s+(\w+)\s*{([^}]+)}/g;
  let match;

  while ((match = modelRegex.exec(schema)) !== null) {
    const modelName = match[1];
    const modelBody = match[2];

    const fields = parseFields(modelBody);

    models.push({
      name: modelName,
      fields: fields,
    });
  }

  return models;
}

function parseEnums(schema) {
  const enums = [];
  const enumRegex = /enum\s+(\w+)\s*{([^}]+)}/g;
  let match;

  while ((match = enumRegex.exec(schema)) !== null) {
    const enumName = match[1];
    const enumBody = match[2];

    const values = enumBody
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line && !line.startsWith('//'))
      .map((line) => line.replace(/,$/, ''));

    enums.push({
      name: enumName,
      values: values,
    });
  }

  return enums;
}

function parseFields(modelBody) {
  const fields = [];
  const lines = modelBody.split('\n');

  for (const line of lines) {
    const trimmed = line.trim();
    if (!trimmed || trimmed.startsWith('//') || trimmed.startsWith('@@')) {
      continue;
    }

    // Skip relation fields and focus on scalar fields
    const fieldMatch = trimmed.match(/^(\w+)\s+([^\s@]+)(\??)\s*(.*)$/);
    if (fieldMatch) {
      const [, name, type, optional, attributes] = fieldMatch;

      // Skip relation fields (they don't have @map or @default typically)
      if (isScalarField(type, attributes)) {
        fields.push({
          name: name,
          type: mapPrismaTypeToTS(type),
          optional: !!optional || type.endsWith('?'),
          isArray: type.includes('[]'),
          attributes: attributes,
        });
      }
    }
  }

  return fields;
}

function isScalarField(type, attributes) {
  // Check if it's a scalar type (not a relation)
  const scalarTypes = [
    'String',
    'Int',
    'Float',
    'Boolean',
    'DateTime',
    'Json',
    'Bytes',
  ];
  const baseType = type.replace('[]', '').replace('?', '');

  // If it's a known scalar type, it's scalar
  if (scalarTypes.includes(baseType)) {
    return true;
  }

  // If it has @map, @default, @id, etc., it's likely scalar
  if (
    attributes &&
    (attributes.includes('@map') ||
      attributes.includes('@default') ||
      attributes.includes('@id'))
  ) {
    return true;
  }

  // If it's an enum (uppercase), it's scalar
  if (baseType.match(/^[A-Z][a-zA-Z]*Enum$/)) {
    return true;
  }

  return false;
}

function mapPrismaTypeToTS(prismaType) {
  // Remove array notation and optional notation for mapping
  const baseType = prismaType.replace('[]', '').replace('?', '');

  const typeMap = {
    String: 'string',
    Int: 'number',
    Float: 'number',
    Boolean: 'boolean',
    DateTime: 'Date',
    Json: 'any',
    Bytes: 'Buffer',
  };

  const mappedType = typeMap[baseType] || baseType;

  // Add array notation back if needed
  return prismaType.includes('[]') ? `${mappedType}[]` : mappedType;
}

function generateTypeScript(models, enums) {
  let output = `// Auto-generated TypeScript types from Prisma schema
// Generated on: ${new Date().toISOString()}
// Do not edit this file manually

`;

  // Generate enums
  output += '// ==================== ENUMS ====================\n\n';

  for (const enumDef of enums) {
    output += `export enum ${enumDef.name} {\n`;
    for (const value of enumDef.values) {
      output += `  ${value} = "${value}",\n`;
    }
    output += '}\n\n';
  }

  // Generate model interfaces
  output += '// ==================== MODELS ====================\n\n';

  for (const model of models) {
    output += `export interface ${model.name} {\n`;

    for (const field of model.fields) {
      const optionalMarker = field.optional ? '?' : '';
      const nullableMarker = field.optional ? ' | null' : '';

      output += `  ${field.name}${optionalMarker}: ${field.type}${nullableMarker};\n`;
    }

    output += '}\n\n';
  }

  // Generate utility types
  output += '// ==================== UTILITY TYPES ====================\n\n';

  output += `export namespace Prisma {
  export type UserGetPayload<T> = User;
  export type EmployeeGetPayload<T> = Employee;
  export type CompanyGetPayload<T> = Company;
  export type JobOfferGetPayload<T> = JobOffer;
  export type ApplicationGetPayload<T> = Application;
  
  // Add more payload types as needed
  export type GetPayload<T, K> = any;
}

`;

  // Add helpful comments
  output += '// ==================== USAGE EXAMPLES ====================\n\n';
  output += `/*
Example usage:

import type { User, Employee, JobOffer } from '@samuelmbabhazi/hr-prisma-types';
import { RoleEnum, EmploymentStatus } from '@samuelmbabhazi/hr-prisma-types';

const user: User = {
  id: "123",
  email: "<EMAIL>",
  role: RoleEnum.EMPLOYEE,
  // ... other properties
};

const employee: Employee = {
  id: "456",
  userId: user.id,
  status: EmploymentStatus.ACTIVE,
  // ... other properties
};
*/`;

  return output;
}
