#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Script pour générer des types autonomes qui ne dépendent pas de @prisma/client
 * Ces types peuvent être utilisés dans des projets frontend sans installer Prisma
 */

console.log('🔄 Génération des types autonomes...');

try {
  // Lire le fichier de types généré par Prisma
  const prismaClientPath = path.join(__dirname, '../node_modules/.pnpm/@prisma+client@4.16.2_prisma@4.16.2/node_modules/@prisma/client/index.d.ts');
  
  let prismaTypes = '';
  
  // Essayer différents chemins possibles pour le client Prisma
  const possiblePaths = [
    path.join(__dirname, '../node_modules/.pnpm/@prisma+client@4.16.2_prisma@4.16.2/node_modules/@prisma/client/index.d.ts'),
    path.join(__dirname, '../node_modules/@prisma/client/index.d.ts'),
    path.join(__dirname, '../node_modules/.pnpm/@prisma+client@*/node_modules/@prisma/client/index.d.ts'),
  ];
  
  let foundPath = null;
  for (const possiblePath of possiblePaths) {
    if (fs.existsSync(possiblePath)) {
      foundPath = possiblePath;
      break;
    }
  }
  
  if (!foundPath) {
    // Chercher dynamiquement
    const nodeModulesPath = path.join(__dirname, '../node_modules');
    if (fs.existsSync(nodeModulesPath)) {
      const pnpmPath = path.join(nodeModulesPath, '.pnpm');
      if (fs.existsSync(pnpmPath)) {
        const pnpmDirs = fs.readdirSync(pnpmPath);
        const prismaClientDir = pnpmDirs.find(dir => dir.startsWith('@prisma+client@'));
        if (prismaClientDir) {
          foundPath = path.join(pnpmPath, prismaClientDir, 'node_modules/@prisma/client/index.d.ts');
        }
      }
    }
  }
  
  if (foundPath && fs.existsSync(foundPath)) {
    prismaTypes = fs.readFileSync(foundPath, 'utf8');
    console.log('✅ Types Prisma trouvés:', foundPath);
  } else {
    console.log('⚠️  Types Prisma non trouvés, génération de types de base...');
    prismaTypes = generateBasicTypes();
  }
  
  // Créer le fichier de types autonomes
  const standaloneTypes = generateStandaloneTypes(prismaTypes);
  
  // Écrire le fichier
  const outputPath = path.join(__dirname, '../src/standalone-types.ts');
  fs.writeFileSync(outputPath, standaloneTypes);
  
  console.log('✅ Types autonomes générés:', outputPath);
  
} catch (error) {
  console.error('❌ Erreur lors de la génération des types autonomes:', error.message);
  
  // Générer des types de base en cas d'erreur
  const basicTypes = generateBasicTypes();
  const outputPath = path.join(__dirname, '../src/standalone-types.ts');
  fs.writeFileSync(outputPath, basicTypes);
  
  console.log('✅ Types de base générés en fallback');
}

function generateBasicTypes() {
  return `// Types de base générés automatiquement
// Ces types sont utilisés quand @prisma/client n'est pas disponible

export interface User {
  id: string;
  email: string;
  password?: string | null;
  googleId?: string | null;
  verified: boolean;
  verificationOtp?: string | null;
  role: RoleEnum;
  passwordReset: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface Profile {
  id: string;
  firstName: string;
  lastName: string;
  avatar?: string | null;
  phoneNumber?: string | null;
  birthDate?: Date | null;
  sex?: string | null;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Employee {
  id: string;
  userId: string;
  status: EmploymentStatus;
  hireDate: Date;
  companyId: string;
  departmentId?: string | null;
  positionId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Company {
  id: string;
  createdById: string;
  companyName: string;
  email: string;
  phoneNumbers: string[];
  website?: string | null;
  logo?: string | null;
  officialName: string;
  taxIdentificationNumber: string;
  industry: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface JobOffer {
  id: string;
  title: string;
  description: string;
  publishDate: Date;
  expirationDate: Date;
  status: JobOfferStatusEnum;
  location: string;
  contractTypes: ContractTypeEnum[];
  minSalary?: number | null;
  maxSalary?: number | null;
  requiredSkills: string[];
  companyId: string;
  departmentId?: string | null;
  positionId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Enums
export enum RoleEnum {
  SUPER_ADMIN = "SUPER_ADMIN",
  ADMIN_HR = "ADMIN_HR",
  ADMIN_RECRUITMENT = "ADMIN_RECRUITMENT",
  OWNER = "OWNER",
  MANAGER = "MANAGER",
  EMPLOYEE = "EMPLOYEE",
  CANDIDATE = "CANDIDATE"
}

export enum EmploymentStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  TERMINATED = "TERMINATED",
  ON_LEAVE = "ON_LEAVE",
  SUSPENDED = "SUSPENDED"
}

export enum JobOfferStatusEnum {
  DRAFT = "DRAFT",
  ACTIVE = "ACTIVE",
  EXPIRED = "EXPIRED",
  FILLED = "FILLED",
  CANCELLED = "CANCELLED"
}

export enum ContractTypeEnum {
  FULL_TIME = "FULL_TIME",
  PART_TIME = "PART_TIME",
  TEMPORARY = "TEMPORARY",
  CONTRACT = "CONTRACT",
  INTERNSHIP = "INTERNSHIP",
  REMOTE = "REMOTE",
  HYBRID = "HYBRID",
  ON_SITE = "ON_SITE",
  SEASONAL = "SEASONAL",
  FREELANCE = "FREELANCE"
}

export enum LeaveTypeEnum {
  ANNUAL = "ANNUAL",
  SICK = "SICK",
  MATERNITY = "MATERNITY",
  UNPAID = "UNPAID"
}

export enum ApplicationStatusEnum {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  HIRED = "HIRED",
  REJECTED = "REJECTED"
}

// Types utilitaires de base
export namespace Prisma {
  export type UserGetPayload<T> = User;
  export type EmployeeGetPayload<T> = Employee;
  export type JobOfferGetPayload<T> = JobOffer;
}
`;
}

function generateStandaloneTypes(prismaTypes) {
  // Extraire les types et enums du fichier Prisma
  // Cette fonction pourrait être plus sophistiquée pour parser le fichier TypeScript
  
  return `// Types autonomes générés automatiquement à partir du schéma Prisma
// Ces types peuvent être utilisés sans installer @prisma/client

${generateBasicTypes()}

// Note: Ces types sont générés automatiquement.
// Pour des types plus complets, installez @prisma/client dans votre projet.
`;
}
