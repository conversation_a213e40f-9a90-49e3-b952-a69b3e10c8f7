// Exemple d'utilisation du package @samuelmbabhazi/hr-prisma-types

import type {
  // Types principaux
  User,
  Employee,
  Company,
  Profile,

  // Types RH
  JobOffer,
  Application,
  Salary,
  Leave,

  // Types Prisma utilitaires
  Prisma,
} from './src/index';

// Import des enums comme valeurs
import {
  RoleEnum,
  EmploymentStatus,
  LeaveTypeEnum,
  ApplicationStatusEnum,
  JobOfferStatusEnum,
} from './src/index';

// Exemple 1: Définir un utilisateur
const user: User = {
  id: '550e8400-e29b-41d4-a716-446655440000',
  email: '<EMAIL>',
  password: null,
  googleId: null,
  verified: true,
  verificationOtp: null,
  role: RoleEnum.EMPLOYEE,
  passwordReset: { otp: null, expiresAt: null },
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Exemple 2: Définir un profil
const profile: Profile = {
  id: '550e8400-e29b-41d4-a716-446655440001',
  firstName: 'John',
  lastName: 'Doe',
  avatar: null,
  phoneNumber: '+243123456789',
  birthDate: new Date('1990-01-01'),
  sex: 'M',
  userId: user.id,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Exemple 3: Définir un employé
const employee: Employee = {
  id: '550e8400-e29b-41d4-a716-446655440002',
  userId: user.id,
  status: EmploymentStatus.ACTIVE,
  hireDate: new Date('2023-01-15'),
  companyId: '550e8400-e29b-41d4-a716-446655440003',
  departmentId: '550e8400-e29b-41d4-a716-446655440004',
  positionId: '550e8400-e29b-41d4-a716-446655440005',
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Exemple 4: Définir une offre d'emploi
const jobOffer: JobOffer = {
  id: '550e8400-e29b-41d4-a716-446655440006',
  title: 'Développeur Full Stack',
  description: 'Nous recherchons un développeur expérimenté...',
  publishDate: new Date(),
  expirationDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 jours
  status: JobOfferStatusEnum.ACTIVE,
  location: 'Kinshasa, RDC',
  contractTypes: ['FULL_TIME', 'REMOTE'],
  minSalary: 800,
  maxSalary: 1500,
  requiredSkills: ['TypeScript', 'React', 'Node.js', 'PostgreSQL'],
  companyId: '550e8400-e29b-41d4-a716-446655440003',
  departmentId: '550e8400-e29b-41d4-a716-446655440004',
  positionId: '550e8400-e29b-41d4-a716-446655440005',
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Exemple 5: Définir une candidature
const application: Application = {
  id: '550e8400-e29b-41d4-a716-446655440007',
  applicationDate: new Date(),
  status: ApplicationStatusEnum.PENDING,
  jobId: jobOffer.id,
  userId: user.id,
  coverLetter: 'Je suis très intéressé par ce poste...',
  resume: 'https://example.com/resume.pdf',
  references: 'Références disponibles sur demande',
  additionalDocuments: ['https://example.com/portfolio.pdf'],
  preferredStartDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 2 semaines
  currentEmploymentStatus: 'Employé',
  desiredSalary: 1200,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Exemple 6: Définir un congé
const leave: Leave = {
  id: '550e8400-e29b-41d4-a716-446655440008',
  leaveType: LeaveTypeEnum.ANNUAL,
  startDate: new Date('2024-03-01'),
  endDate: new Date('2024-03-15'),
  reason: 'Vacances familiales',
  status: 'REQUESTED',
  employeeId: employee.id,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Exemple 7: Utilisation des types Prisma pour les requêtes
type UserWithProfile = Prisma.UserGetPayload<{
  include: {
    profile: true;
    employee: {
      include: {
        department: true;
        position: true;
      };
    };
  };
}>;

type JobOfferWithCompany = Prisma.JobOfferGetPayload<{
  include: {
    company: true;
    department: true;
    position: true;
    applications: {
      include: {
        user: {
          include: {
            profile: true;
          };
        };
      };
    };
  };
}>;

// Exemple 8: Types pour les formulaires frontend
interface CreateJobOfferForm {
  title: string;
  description: string;
  location: string;
  contractTypes: string[];
  minSalary?: number;
  maxSalary?: number;
  requiredSkills: string[];
  departmentId?: string;
  positionId?: string;
  expirationDate: Date;
}

interface CreateApplicationForm {
  jobId: string;
  coverLetter: string;
  resume: string;
  references?: string;
  additionalDocuments?: string[];
  preferredStartDate?: Date;
  currentEmploymentStatus?: string;
  desiredSalary?: number;
}

// Exemple 9: Types pour les réponses API
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

type GetUsersResponse = ApiResponse<User[]>;
type GetJobOffersResponse = ApiResponse<JobOfferWithCompany[]>;
type CreateApplicationResponse = ApiResponse<Application>;

// Exemple 10: Fonctions utilitaires avec types
function isActiveEmployee(employee: Employee): boolean {
  return employee.status === EmploymentStatus.ACTIVE;
}

function canApplyToJob(jobOffer: JobOffer): boolean {
  return (
    jobOffer.status === JobOfferStatusEnum.ACTIVE &&
    jobOffer.expirationDate > new Date()
  );
}

function getLeaveStatusColor(status: string): string {
  switch (status) {
    case 'APPROVED':
      return 'green';
    case 'REJECTED':
      return 'red';
    case 'PENDING':
      return 'orange';
    default:
      return 'gray';
  }
}

export type {
  UserWithProfile,
  JobOfferWithCompany,
  CreateJobOfferForm,
  CreateApplicationForm,
  ApiResponse,
  GetUsersResponse,
  GetJobOffersResponse,
  CreateApplicationResponse,
};

export { isActiveEmployee, canApplyToJob, getLeaveStatusColor };
