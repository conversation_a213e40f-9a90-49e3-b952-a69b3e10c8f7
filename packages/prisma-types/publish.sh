#!/bin/bash

# Script de publication automatisé pour le package @samuelmbabhazi/hr-prisma-types

set -e  # Arrêter en cas d'erreur

echo "🚀 Publication du package @samuelmbabhazi/hr-prisma-types"

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "package.json" ]; then
    echo "❌ Erreur: package.json non trouvé. Assurez-vous d'être dans le répertoire du package."
    exit 1
fi

# Vérifier que le package.json contient le bon nom
if ! grep -q "@samuelmbabhazi/hr-prisma-types" package.json; then
    echo "❌ Erreur: Ce n'est pas le bon package."
    exit 1
fi

# Demander le type de version
echo "Quel type de mise à jour de version souhaitez-vous ?"
echo "1) patch (1.0.0 -> 1.0.1)"
echo "2) minor (1.0.0 -> 1.1.0)"
echo "3) major (1.0.0 -> 2.0.0)"
echo "4) Au<PERSON>ne (utiliser la version actuelle)"
read -p "Votre choix (1-4): " choice

case $choice in
    1)
        echo "📈 Mise à jour patch..."
        npm version patch
        ;;
    2)
        echo "📈 Mise à jour minor..."
        npm version minor
        ;;
    3)
        echo "📈 Mise à jour major..."
        npm version major
        ;;
    4)
        echo "📦 Utilisation de la version actuelle..."
        ;;
    *)
        echo "❌ Choix invalide. Annulation."
        exit 1
        ;;
esac

# Mettre à jour le schéma depuis le projet principal
echo "🔄 Mise à jour du schéma Prisma..."
if [ -f "../../prisma/schema.prisma" ]; then
    cp ../../prisma/schema.prisma ./prisma/schema.prisma
    echo "✅ Schéma mis à jour"
else
    echo "⚠️  Attention: Schéma principal non trouvé, utilisation du schéma actuel"
fi

# Nettoyer et reconstruire
echo "🧹 Nettoyage..."
npm run clean

echo "🔨 Construction du package..."
npm run build

# Vérifier que la construction a réussi
if [ ! -f "dist/index.d.ts" ]; then
    echo "❌ Erreur: La construction a échoué."
    exit 1
fi

echo "✅ Construction réussie"

# Tester la compilation de l'exemple
echo "🧪 Test de l'exemple..."
if npx tsc --noEmit example.ts; then
    echo "✅ Exemple compilé avec succès"
else
    echo "❌ Erreur: L'exemple ne compile pas."
    exit 1
fi

# Afficher les informations du package
echo "📋 Informations du package:"
npm list --depth=0

# Demander confirmation pour la publication
echo ""
echo "📦 Prêt à publier le package avec les informations suivantes:"
echo "   Nom: $(npm pkg get name | tr -d '"')"
echo "   Version: $(npm pkg get version | tr -d '"')"
echo ""
read -p "Voulez-vous continuer avec la publication ? (y/N): " confirm

if [[ $confirm =~ ^[Yy]$ ]]; then
    echo "🚀 Publication en cours..."
    
    # Vérifier que l'utilisateur est connecté à npm
    if ! npm whoami > /dev/null 2>&1; then
        echo "❌ Erreur: Vous devez être connecté à npm. Utilisez 'npm login'."
        exit 1
    fi
    
    # Publier le package
    npm publish --access public
    
    if [ $? -eq 0 ]; then
        echo "🎉 Package publié avec succès !"
        echo "📦 Vous pouvez maintenant l'installer avec:"
        echo "   npm install @samuelmbabhazi/hr-prisma-types"
    else
        echo "❌ Erreur lors de la publication."
        exit 1
    fi
else
    echo "❌ Publication annulée."
    exit 0
fi
