// Test d'utilisation du package dans un projet frontend
import type { 
  User, 
  Employee, 
  <PERSON>O<PERSON>,
  <PERSON><PERSON><PERSON> 
} from '../dist/index';

import { 
  RoleEnum, 
  EmploymentStatus, 
  JobOfferStatusEnum 
} from '../dist/index';

// Test 1: Utilisation des types de base
const user: User = {
  id: "550e8400-e29b-41d4-a716-446655440000",
  email: "<EMAIL>",
  password: null,
  googleId: null,
  verified: true,
  verificationOtp: null,
  role: RoleEnum.EMPLOYEE,
  passwordReset: { otp: null, expiresAt: null },
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Test 2: Utilisation des enums
const employee: Employee = {
  id: "550e8400-e29b-41d4-a716-446655440001",
  userId: user.id,
  status: EmploymentStatus.ACTIVE,
  hireDate: new Date(),
  companyId: "550e8400-e29b-41d4-a716-446655440002",
  departmentId: null,
  positionId: null,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Test 3: Utilisation des types complexes
const jobOffer: JobOffer = {
  id: "550e8400-e29b-41d4-a716-446655440003",
  title: "Développeur Frontend",
  description: "Poste de développeur React/TypeScript",
  publishDate: new Date(),
  expirationDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
  status: JobOfferStatusEnum.ACTIVE,
  location: "Kinshasa, RDC",
  contractTypes: ["FULL_TIME", "REMOTE"],
  minSalary: 800,
  maxSalary: 1500,
  requiredSkills: ["React", "TypeScript", "CSS"],
  companyId: "550e8400-e29b-41d4-a716-446655440002",
  departmentId: null,
  positionId: null,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Test 4: Utilisation des types Prisma utilitaires
type UserWithProfile = Prisma.UserGetPayload<{
  include: {
    profile: true;
  };
}>;

// Test 5: Fonctions utilitaires
function isActiveEmployee(emp: Employee): boolean {
  return emp.status === EmploymentStatus.ACTIVE;
}

function canApplyToJob(job: JobOffer): boolean {
  return job.status === JobOfferStatusEnum.ACTIVE && 
         job.expirationDate > new Date();
}

// Test 6: Interface pour API
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

type GetUsersResponse = ApiResponse<User[]>;
type GetJobOffersResponse = ApiResponse<JobOffer[]>;

// Tests de validation
console.log('✅ Test 1: Types de base - OK');
console.log('✅ Test 2: Enums - OK');
console.log('✅ Test 3: Types complexes - OK');
console.log('✅ Test 4: Types Prisma utilitaires - OK');
console.log('✅ Test 5: Fonctions utilitaires - OK');
console.log('✅ Test 6: Interfaces API - OK');

console.log('🎉 Tous les tests passent ! Le package fonctionne correctement.');

export {
  user,
  employee,
  jobOffer,
  isActiveEmployee,
  canApplyToJob,
  type UserWithProfile,
  type GetUsersResponse,
  type GetJobOffersResponse,
};
