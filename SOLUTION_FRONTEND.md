# Solution pour l'erreur "Cannot find module '@prisma/client'"

## 🚨 Problème
Quand vous utilisez `@samuelmbabhazi/hr-prisma-types` dans votre projet frontend, vous obtenez l'erreur :
```
Cannot find module '@prisma/client' or its corresponding type declarations.
```

## ✅ Solution Simple

### Étape 1: Installer @prisma/client dans votre projet frontend

```bash
npm install @prisma/client
# ou
yarn add @prisma/client
# ou
pnpm add @prisma/client
```

### Étape 2: Utiliser les types normalement

```typescript
// ✅ Ceci fonctionnera maintenant
import type { User, Employee, JobOffer } from '@samuelmbabhazi/hr-prisma-types';
import { RoleEnum, EmploymentStatus } from '@samuelmbabhazi/hr-prisma-types';

const user: User = {
  id: "123",
  email: "<EMAIL>",
  role: RoleEnum.EMPLOYEE,
  // ... autres propriétés
};
```

## 🤔 Pourquoi cette solution ?

1. **Notre package dépend de @prisma/client** pour les définitions de types
2. **@prisma/client contient uniquement les types TypeScript** dans votre frontend
3. **Aucun code runtime de Prisma** n'est ajouté à votre bundle
4. **Les bundlers modernes** (Webpack, Vite, etc.) excluent automatiquement les types

## 🛡️ Assurance qualité

### Vérifier que Prisma n'est pas dans votre bundle

**Avec Webpack Bundle Analyzer :**
```bash
npm install --save-dev webpack-bundle-analyzer
# Analyser votre bundle - vous ne devriez pas voir @prisma/client
```

**Avec Vite :**
```bash
npm run build
# Vérifier la taille du bundle - elle ne devrait pas inclure Prisma
```

### Configuration optionnelle pour être sûr

**Webpack (optionnel) :**
```javascript
// webpack.config.js
module.exports = {
  externals: {
    '@prisma/client': 'commonjs @prisma/client'
  }
};
```

**Vite (optionnel) :**
```javascript
// vite.config.js
export default {
  build: {
    rollupOptions: {
      external: ['@prisma/client']
    }
  }
};
```

## 📦 Installation complète recommandée

```bash
# Dans votre projet frontend
npm install @samuelmbabhazi/hr-prisma-types @prisma/client
```

## 🎯 Exemple complet d'utilisation

```typescript
// components/UserCard.tsx
import React from 'react';
import type { User, Profile } from '@samuelmbabhazi/hr-prisma-types';
import { RoleEnum } from '@samuelmbabhazi/hr-prisma-types';

interface UserCardProps {
  user: User;
  profile: Profile;
}

export const UserCard: React.FC<UserCardProps> = ({ user, profile }) => {
  const isAdmin = user.role === RoleEnum.ADMIN_HR || user.role === RoleEnum.SUPER_ADMIN;
  
  return (
    <div className="user-card">
      <h3>{profile.firstName} {profile.lastName}</h3>
      <p>Email: {user.email}</p>
      <p>Rôle: {user.role}</p>
      {isAdmin && <span className="admin-badge">Admin</span>}
    </div>
  );
};
```

## ✨ Avantages de cette approche

1. **Types toujours synchronisés** avec votre API
2. **Aucun impact sur la taille du bundle** frontend
3. **IntelliSense complet** dans votre IDE
4. **Validation TypeScript** à la compilation
5. **Enums utilisables en runtime** pour la logique métier

Cette solution est la plus propre et la plus maintenable pour utiliser vos types Prisma côté frontend !
